# -*- coding: utf-8 -*-

"""
TimeNest 主题管理器
支持主题加载、切换、市场扩展

该模块提供了完整的主题管理功能，包括：
- 主题加载和验证
- 主题切换和应用
- 主题市场集成
- 自定义主题支持
- 主题缓存和优化
"""

import hashlib
import json
import logging
from dataclasses import asdict, dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from utils.common_imports import QObject, Signal
from utils.data_processing import safe_json_load, safe_json_save
from utils.shared_utilities import validate_path
from utils.config_constants import DEFAULT_THEME_SETTINGS

try:
    from PySide6.QtGui import QColor, QPalette
    QT_GUI_AVAILABLE = True
except ImportError:
    logging.error("PySide6 GUI components not available")
    QT_GUI_AVAILABLE = False

    class QColor:
        def __init__(self, *args):
            pass

    class QPalette:
        pass


@dataclass
class ThemeInfo:
    """
    主题信息数据类

    Attributes:
        name: 主题名称
        version: 主题版本
        author: 主题作者
        description: 主题描述
        colors: 颜色配置
        styles: 样式配置
        fonts: 字体配置
        icons: 图标配置
        preview: 预览图路径
        tags: 主题标签
        created_at: 创建时间
        updated_at: 更新时间
    """
    name: str
    version: str = "1.0.0"
    author: str = "Unknown"
    description: str = ""
    colors: Dict[str, str] = None
    styles: Dict[str, Any] = None
    fonts: Dict[str, str] = None
    icons: Dict[str, str] = None
    preview: str = ""
    tags: List[str] = None
    created_at: str = ""
    updated_at: str = ""

    def __post_init__(self):
        if self.colors is None:
            self.colors = {}
        if self.styles is None:
            self.styles = {}
        if self.fonts is None:
            self.fonts = {}
        if self.icons is None:
            self.icons = {}
        if self.tags is None:
            self.tags = []


class ThemeManager(QObject):
    """
    TimeNest 主题管理器

    提供完整的主题管理功能，支持主题加载、切换、验证和市场集成。

    Attributes:
        theme_dir: 主题文件目录
        themes: 已加载的主题字典
        current_theme: 当前应用的主题名称
        theme_cache: 主题缓存

    Signals:
        theme_changed: 主题变更信号 (theme_name: str)
        theme_loaded: 主题加载完成信号 (theme_count: int)
        theme_error: 主题错误信号 (error_message: str)
    """

    # 信号定义
    theme_changed = Signal(str)  # 主题名称
    theme_loaded = Signal(int)   # 主题数量
    theme_error = Signal(str)    # 错误信息

    def __init__(self, theme_dir: str = "themes") -> None:
        """
        初始化主题管理器

        Args:
            theme_dir: 主题文件目录路径
        """
        super().__init__()

        try:
            self.logger = logging.getLogger(f'{__name__}.ThemeManager')
            self.theme_dir = validate_path(theme_dir, create_if_missing=True) or Path(theme_dir)
            self.themes: Dict[str, ThemeInfo] = {}
            self.current_theme: Optional[str] = None
            self.theme_cache: Dict[str, str] = {}

            self._load_default_themes()
            self.load_themes()

            self.logger.info(f"主题管理器初始化完成，主题目录: {self.theme_dir}")

        except Exception as e:
            self.logger.error(f"主题管理器初始化失败: {e}")
            self._load_fallback_theme()

    def _load_fallback_theme(self):
        """加载后备主题"""
        try:
            fallback_theme = ThemeInfo(
                name="fallback",
                version="1.0.0",
                author="TimeNest",
                description="后备主题",
                colors=DEFAULT_THEME_SETTINGS,
                styles={},
                fonts={}
            )
            self.themes["fallback"] = fallback_theme
            self.current_theme = "fallback"
            self.logger.info("已加载后备主题")
        except Exception as e:
            self.logger.error(f"加载后备主题失败: {e}")

        except (OSError, PermissionError) as e:
            error_msg = f"主题管理器初始化失败: {e}"
            self.logger.error(error_msg)
            self.theme_error.emit(error_msg)
            raise

    def _load_default_themes(self):
        """加载默认主题"""
        try:
            # 创建默认的浅色主题
            light_theme = {
                "name": "浅色主题",
                "type": "light",
                "colors": {
                    "primary": "#2196F3",
                    "secondary": "#FFC107",
                    "background": "#FFFFFF",
                    "surface": "#F5F5F5",
                    "text": "#000000",
                    "text_secondary": "#666666"
                }
            }

            # 创建默认的深色主题
            dark_theme = {
                "name": "深色主题",
                "type": "dark",
                "colors": {
                    "primary": "#2196F3",
                    "secondary": "#FFC107",
                    "background": "#121212",
                    "surface": "#1E1E1E",
                    "text": "#FFFFFF",
                    "text_secondary": "#CCCCCC"
                }
            }

            # 保存默认主题到文件
            light_theme_file = self.theme_dir / "light.json"
            dark_theme_file = self.theme_dir / "dark.json"

            if not light_theme_file.exists():
                with open(light_theme_file, 'w', encoding='utf-8') as f:
                    json.dump(light_theme, f, ensure_ascii=False, indent=2)

            if not dark_theme_file.exists():
                with open(dark_theme_file, 'w', encoding='utf-8') as f:
                    json.dump(dark_theme, f, ensure_ascii=False, indent=2)

            self.logger.info("默认主题加载完成")

        except Exception as e:
            self.logger.error(f"加载默认主题失败: {e}")

    def load_themes(self):
        """加载本地主题"""
        self.themes.clear()
        if not self.theme_dir.exists():
            self.theme_dir.mkdir(parents=True, exist_ok=True)
        for theme_file in self.theme_dir.glob("*.json"):
            try:
                with open(theme_file, 'r', encoding='utf-8') as f:
                    theme_data = json.load(f)
                theme_name = theme_data.get("name") or theme_file.stem
                self.themes[theme_name] = theme_data
            except Exception as e:
                self.logger.error(f"加载主题失败: {theme_file}: {e}")

    def apply_theme(self, theme_name: str) -> bool:
        """应用指定主题"""
        if theme_name not in self.themes:
            self.logger.error(f"主题不存在: {theme_name}")
            return False
        theme = self.themes[theme_name]
        # 这里可扩展为应用 QSS/CSS/配色等
        self.current_theme = theme_name
        self.logger.info(f"已应用主题: {theme_name}")
        return True

    def get_theme_list(self):
        """获取所有主题名称"""
        return list(self.themes.keys())

    def fetch_market_themes(self, market_url: str) -> Dict[str, dict]:
        """从主题市场拉取主题列表（预留，需实现网络请求）"""
        # 可用 requests/urllib 实现在线主题市场拉取
        self.logger.info(f"拉取主题市场: {market_url}")
        return {}
