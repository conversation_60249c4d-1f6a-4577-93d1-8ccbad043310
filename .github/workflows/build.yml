name: Build Development Version

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Syntax check
      run: |
        python -m py_compile main.py
        python -c "import py_compile; import os; [py_compile.compile(os.path.join(root, file), doraise=True) for root, dirs, files in os.walk('.') if 'venv' not in root and '__pycache__' not in root for file in files if file.endswith('.py')]"

    - name: Set UTF-8 encoding
      if: github.ref == 'refs/heads/main'
      run: |
        $OutputEncoding = [System.Text.Encoding]::UTF8
        [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
        $env:PYTHONIOENCODING = "utf-8"
      shell: pwsh

    - name: Build executable (if on main branch)
      if: github.ref == 'refs/heads/main'
      run: |
        $OutputEncoding = [System.Text.Encoding]::UTF8
        [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
        $env:PYTHONIOENCODING = "utf-8"

        pip install pyinstaller
        if (Test-Path "TimeNest.spec") {
          echo "Building dev version with spec file..."
          pyinstaller TimeNest.spec --clean --noconfirm
          if (Test-Path "dist\TimeNest.exe") {
            Rename-Item "dist\TimeNest.exe" "dist\TimeNest-dev.exe"
          }
        } else {
          echo "Building dev version with simple command..."
          pyinstaller --onefile --windowed --name TimeNest-dev main.py
        }
      shell: pwsh
        
    - name: Upload build artifact
      if: github.ref == 'refs/heads/main'
      uses: actions/upload-artifact@v4
      with:
        name: TimeNest-dev-build
        path: dist/TimeNest-dev.exe
        retention-days: 7