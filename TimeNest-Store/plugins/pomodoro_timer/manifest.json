{"id": "pomodoro_timer", "name": "番茄钟插件", "version": "2.1.0", "description": "专业的番茄工作法计时器，帮助提高工作效率和专注力", "author": "Productivity Team", "plugin_class": "PomodoroTimerPlugin", "plugin_type": "utility", "api_version": "1.0.0", "min_app_version": "1.0.0", "max_app_version": "", "homepage": "https://github.com/ziyi127/TimeNest-Store/tree/main/plugins/pomodoro_timer", "repository": "https://github.com/ziyi127/TimeNest-Store", "license": "MIT", "tags": ["productivity", "timer", "focus"], "dependencies": [], "permissions": ["notification_access", "config_access"], "settings": {"work_duration": {"type": "integer", "default": 25, "min": 5, "max": 60, "description": "工作时长（分钟）"}, "short_break": {"type": "integer", "default": 5, "min": 1, "max": 15, "description": "短休息时长（分钟）"}, "long_break": {"type": "integer", "default": 15, "min": 5, "max": 30, "description": "长休息时长（分钟）"}, "cycles_before_long_break": {"type": "integer", "default": 4, "min": 2, "max": 8, "description": "长休息前的工作周期数"}, "auto_start_breaks": {"type": "boolean", "default": false, "description": "自动开始休息"}, "auto_start_work": {"type": "boolean", "default": false, "description": "自动开始工作"}, "sound_enabled": {"type": "boolean", "default": true, "description": "启用声音提醒"}, "notification_enabled": {"type": "boolean", "default": true, "description": "启用桌面通知"}, "show_in_floating": {"type": "boolean", "default": true, "description": "在浮窗中显示计时器"}}, "changelog": {"2.1.0": "新增自定义时长设置，优化通知提醒", "2.0.0": "重构界面，新增统计功能", "1.0.0": "初始版本发布"}}