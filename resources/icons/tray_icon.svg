<?xml version="1.0" encoding="UTF-8"?>
<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect x="10" y="10" width="160" height="160" rx="35" ry="35" fill="url(#gradient)" filter="url(#shadow)"/>
  
  <!-- Clock face -->
  <circle cx="90" cy="90" r="55" fill="none" stroke="#ffffff" stroke-width="6"/>
  
  <!-- Hour markers -->
  <circle cx="90" cy="45" r="4" fill="#ffffff"/>
  <circle cx="135" cy="90" r="4" fill="#ffffff"/>
  <circle cx="90" cy="135" r="4" fill="#ffffff"/>
  <circle cx="45" cy="90" r="4" fill="#ffffff"/>
  
  <!-- Additional hour markers -->
  <circle cx="115" cy="60" r="2.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="120" cy="115" r="2.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="65" cy="120" r="2.5" fill="#ffffff" opacity="0.8"/>
  <circle cx="60" cy="65" r="2.5" fill="#ffffff" opacity="0.8"/>
  
  <!-- Clock hands -->
  <line x1="90" y1="90" x2="90" y2="55" stroke="#ffffff" stroke-width="6" stroke-linecap="round"/>
  <line x1="90" y1="90" x2="115" y2="90" stroke="#ffffff" stroke-width="4" stroke-linecap="round"/>
  
  <!-- Center dot -->
  <circle cx="90" cy="90" r="6" fill="#ffffff"/>
  
  <!-- Decorative elements -->
  <circle cx="90" cy="90" r="70" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
  <circle cx="90" cy="90" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
</svg>
