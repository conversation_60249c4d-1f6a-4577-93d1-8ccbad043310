# TimeNest 2.2.0 Release 项目依赖
# Python 智能时间管理助手 - RinUI版本

# ===== 核心运行时依赖 =====

# 核心GUI框架
RinUI>=0.1.5
PySide6>=6.6.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0

# 配置文件处理
PyYAML>=6.0.1

# Excel文件处理
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# 时间处理
python-dateutil>=2.8.2

# 网络请求（天气组件）
requests>=2.31.0

# 系统通知
plyer>=2.1.0

# 图标和图像处理
Pillow>=10.0.0

# 日志增强
coloredlogs>=15.0.1

# 配置验证
jsonschema>=4.19.0

# 包版本管理（插件系统需要）
packaging>=23.0

# 加密（配置文件保护）
cryptography>=41.0.0

# 系统信息监控（性能管理器）
psutil>=5.9.0

# 错误监控和日志（可选）
sentry-sdk>=1.32.0

# ===== 构建和发布依赖 =====

# 可执行文件打包
pyinstaller>=5.13.0

# 版本管理
setuptools>=68.0.0
wheel>=0.41.0

# ===== 注意事项 =====
#
# 1. 这是核心运行时依赖文件，包含应用运行和构建必需的包
# 2. 开发工具请安装 requirements-dev.txt: pip install -r requirements-dev.txt
# 3. 完整安装请使用: pip install -r requirements.txt -r requirements-dev.txt
# 4. 生产环境建议只安装运行时依赖，构建环境需要完整依赖
# 5. 自动化构建已集成在 GitHub Actions 和本地脚本中
#
# 安装命令:
# pip install -r requirements.txt
#
# 构建命令:
# python release.py "2.2.0 Release"
# build_release.bat