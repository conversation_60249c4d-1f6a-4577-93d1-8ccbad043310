#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TimeNest 集群客户端
运行在每个TimeNest实例上，与集群管理器通信
"""

import asyncio
import json
import logging
import time
import uuid
import socket
import psutil
import platform
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import websockets
from cryptography.fernet import Fernet

class ClusterClient:
    """集群客户端"""
    
    def __init__(self, manager_host: str = "localhost", manager_port: int = 8765):
        self.manager_host = manager_host
        self.manager_port = manager_port
        self.node_id = self._generate_node_id()
        self.websocket = None
        self.running = False
        self.encryption_key = None
        self.cipher = None
        
        # 节点信息
        self.hostname = socket.gethostname()
        self.ip_address = self._get_local_ip()
        self.port = 8080  # TimeNest应用端口
        self.version = "2.2.2"
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('cluster/cluster_client.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def _generate_node_id(self) -> str:
        """生成节点ID"""
        # 使用MAC地址和主机名生成唯一ID
        mac = hex(uuid.getnode())[2:]
        hostname = socket.gethostname()
        return f"{hostname}-{mac}"
    
    def _get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            # 连接到外部地址获取本地IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"
    
    def _load_encryption_key(self) -> Optional[bytes]:
        """加载加密密钥"""
        key_file = Path("cluster/cluster.key")
        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        return None
    
    def encrypt_message(self, message: str) -> str:
        """加密消息"""
        if self.cipher:
            return self.cipher.encrypt(message.encode()).decode()
        return message
    
    def decrypt_message(self, encrypted_message: str) -> str:
        """解密消息"""
        if self.cipher:
            try:
                return self.cipher.decrypt(encrypted_message.encode()).decode()
            except Exception:
                raise ValueError("解密失败")
        return encrypted_message
    
    async def connect_to_manager(self):
        """连接到集群管理器"""
        uri = f"ws://{self.manager_host}:{self.manager_port}"
        
        try:
            self.websocket = await websockets.connect(uri)
            self.logger.info(f"已连接到集群管理器: {uri}")
            
            # 加载加密密钥
            self.encryption_key = self._load_encryption_key()
            if self.encryption_key:
                self.cipher = Fernet(self.encryption_key)
            
            # 注册节点
            await self.register_node()
            
            return True
            
        except Exception as e:
            self.logger.error(f"连接集群管理器失败: {e}")
            return False
    
    async def register_node(self):
        """注册节点到集群"""
        register_data = {
            "type": "register",
            "node_id": self.node_id,
            "hostname": self.hostname,
            "ip_address": self.ip_address,
            "port": self.port,
            "version": self.version,
            "cpu_usage": self.get_cpu_usage(),
            "memory_usage": self.get_memory_usage(),
            "disk_usage": self.get_disk_usage(),
            "active_users": self.get_active_users(),
            "uptime": self.get_uptime()
        }
        
        encrypted_message = self.encrypt_message(json.dumps(register_data))
        await self.websocket.send(encrypted_message)
        
        # 等待注册响应
        response = await self.websocket.recv()
        decrypted_response = self.decrypt_message(response)
        response_data = json.loads(decrypted_response)
        
        if response_data.get("status") == "success":
            self.logger.info("节点注册成功")
        else:
            self.logger.error(f"节点注册失败: {response_data.get('message')}")
    
    async def start_client(self):
        """启动客户端"""
        self.running = True
        
        while self.running:
            try:
                if not self.websocket or self.websocket.closed:
                    if await self.connect_to_manager():
                        # 启动后台任务
                        asyncio.create_task(self.heartbeat_sender())
                        asyncio.create_task(self.metrics_sender())
                        asyncio.create_task(self.message_handler())
                
                await asyncio.sleep(5)
                
            except Exception as e:
                self.logger.error(f"客户端运行错误: {e}")
                await asyncio.sleep(10)
    
    async def stop_client(self):
        """停止客户端"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
        self.logger.info("集群客户端已停止")
    
    async def heartbeat_sender(self):
        """发送心跳"""
        while self.running and self.websocket and not self.websocket.closed:
            try:
                heartbeat_data = {
                    "type": "heartbeat",
                    "node_id": self.node_id,
                    "cpu_usage": self.get_cpu_usage(),
                    "memory_usage": self.get_memory_usage(),
                    "disk_usage": self.get_disk_usage(),
                    "active_users": self.get_active_users(),
                    "uptime": self.get_uptime()
                }
                
                encrypted_message = self.encrypt_message(json.dumps(heartbeat_data))
                await self.websocket.send(encrypted_message)
                
                await asyncio.sleep(30)  # 每30秒发送一次心跳
                
            except Exception as e:
                self.logger.error(f"发送心跳失败: {e}")
                break
    
    async def metrics_sender(self):
        """发送指标数据"""
        while self.running and self.websocket and not self.websocket.closed:
            try:
                metrics = self.collect_detailed_metrics()
                
                metrics_data = {
                    "type": "metrics",
                    "node_id": self.node_id,
                    "metrics": metrics
                }
                
                encrypted_message = self.encrypt_message(json.dumps(metrics_data))
                await self.websocket.send(encrypted_message)
                
                await asyncio.sleep(300)  # 每5分钟发送一次详细指标
                
            except Exception as e:
                self.logger.error(f"发送指标失败: {e}")
                break
    
    async def message_handler(self):
        """处理来自管理器的消息"""
        while self.running and self.websocket and not self.websocket.closed:
            try:
                message = await self.websocket.recv()
                decrypted_message = self.decrypt_message(message)
                data = json.loads(decrypted_message)
                
                await self.process_manager_message(data)
                
            except websockets.exceptions.ConnectionClosed:
                self.logger.info("与管理器的连接已断开")
                break
            except Exception as e:
                self.logger.error(f"处理管理器消息失败: {e}")
    
    async def process_manager_message(self, data: Dict[str, Any]):
        """处理管理器消息"""
        message_type = data.get("type")
        
        if message_type == "command":
            await self.handle_command(data)
        elif message_type == "config_update":
            await self.handle_config_update(data)
        elif message_type == "restart":
            await self.handle_restart()
        elif message_type == "shutdown":
            await self.handle_shutdown()
        else:
            self.logger.warning(f"未知消息类型: {message_type}")
    
    async def handle_command(self, data: Dict[str, Any]):
        """处理命令"""
        command = data.get("command")
        
        if command == "get_status":
            status = self.get_node_status()
            await self.send_response("status", status)
        elif command == "get_logs":
            logs = self.get_recent_logs()
            await self.send_response("logs", logs)
        elif command == "clear_cache":
            await self.clear_cache()
            await self.send_response("cache_cleared", {"status": "success"})
        else:
            self.logger.warning(f"未知命令: {command}")
    
    async def handle_config_update(self, data: Dict[str, Any]):
        """处理配置更新"""
        config = data.get("config", {})
        
        # 更新本地配置
        await self.update_local_config(config)
        
        self.logger.info("配置已更新")
        await self.send_response("config_updated", {"status": "success"})
    
    async def handle_restart(self):
        """处理重启命令"""
        self.logger.info("收到重启命令")
        
        # 这里可以实现重启TimeNest应用的逻辑
        # 例如：重新加载配置、重启服务等
        
        await self.send_response("restart_completed", {"status": "success"})
    
    async def handle_shutdown(self):
        """处理关闭命令"""
        self.logger.info("收到关闭命令")
        
        await self.send_response("shutdown_initiated", {"status": "success"})
        
        # 优雅关闭
        await self.stop_client()
    
    async def send_response(self, response_type: str, data: Dict[str, Any]):
        """发送响应"""
        response = {
            "type": "response",
            "response_type": response_type,
            "node_id": self.node_id,
            "data": data,
            "timestamp": time.time()
        }
        
        encrypted_message = self.encrypt_message(json.dumps(response))
        await self.websocket.send(encrypted_message)
    
    def get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        return psutil.cpu_percent(interval=1)
    
    def get_memory_usage(self) -> float:
        """获取内存使用率"""
        memory = psutil.virtual_memory()
        return memory.percent
    
    def get_disk_usage(self) -> float:
        """获取磁盘使用率"""
        disk = psutil.disk_usage('/')
        return (disk.used / disk.total) * 100
    
    def get_active_users(self) -> int:
        """获取活跃用户数"""
        # 这里可以实现获取TimeNest活跃用户数的逻辑
        return len(psutil.users())
    
    def get_uptime(self) -> float:
        """获取系统运行时间"""
        return time.time() - psutil.boot_time()
    
    def collect_detailed_metrics(self) -> Dict[str, Any]:
        """收集详细指标"""
        return {
            "system": {
                "platform": platform.platform(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "python_version": platform.python_version()
            },
            "cpu": {
                "usage_percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent,
                "used": psutil.virtual_memory().used
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "used": psutil.disk_usage('/').used,
                "free": psutil.disk_usage('/').free,
                "percent": (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100
            },
            "network": {
                "bytes_sent": psutil.net_io_counters().bytes_sent,
                "bytes_recv": psutil.net_io_counters().bytes_recv,
                "packets_sent": psutil.net_io_counters().packets_sent,
                "packets_recv": psutil.net_io_counters().packets_recv
            },
            "processes": len(psutil.pids()),
            "users": len(psutil.users()),
            "boot_time": psutil.boot_time(),
            "timestamp": time.time()
        }
    
    def get_node_status(self) -> Dict[str, Any]:
        """获取节点状态"""
        return {
            "node_id": self.node_id,
            "hostname": self.hostname,
            "ip_address": self.ip_address,
            "version": self.version,
            "status": "online",
            "uptime": self.get_uptime(),
            "cpu_usage": self.get_cpu_usage(),
            "memory_usage": self.get_memory_usage(),
            "disk_usage": self.get_disk_usage(),
            "active_users": self.get_active_users(),
            "timestamp": time.time()
        }
    
    def get_recent_logs(self, lines: int = 100) -> list:
        """获取最近的日志"""
        log_file = Path("cluster/cluster_client.log")
        
        if log_file.exists():
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        return []
    
    async def clear_cache(self):
        """清理缓存"""
        # 这里可以实现清理TimeNest缓存的逻辑
        cache_dir = Path("cache")
        if cache_dir.exists():
            import shutil
            shutil.rmtree(cache_dir)
            cache_dir.mkdir()
        
        self.logger.info("缓存已清理")
    
    async def update_local_config(self, config: Dict[str, Any]):
        """更新本地配置"""
        config_file = Path("cluster/node_config.json")
        config_file.parent.mkdir(exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        self.logger.info("本地配置已更新")

# 主函数
async def main():
    """主函数"""
    # 从配置文件读取管理器地址
    config_file = Path("cluster/client_config.json")
    
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            manager_host = config.get("manager_host", "localhost")
            manager_port = config.get("manager_port", 8765)
    else:
        manager_host = "localhost"
        manager_port = 8765
        
        # 创建默认配置
        default_config = {
            "manager_host": manager_host,
            "manager_port": manager_port,
            "auto_start": True,
            "retry_interval": 10
        }
        
        config_file.parent.mkdir(exist_ok=True)
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
    
    client = ClusterClient(manager_host, manager_port)
    
    try:
        await client.start_client()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭客户端...")
    finally:
        await client.stop_client()

if __name__ == "__main__":
    asyncio.run(main())
