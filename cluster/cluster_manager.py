#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TimeNest 集群管理器
负责管理多个TimeNest实例的中央控制系统
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import websockets
import aiohttp
from cryptography.fernet import Fernet

@dataclass
class NodeInfo:
    """节点信息"""
    node_id: str
    hostname: str
    ip_address: str
    port: int
    version: str
    status: str  # online, offline, error, maintenance
    last_heartbeat: float
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_users: int
    uptime: float
    
class ClusterManager:
    """集群管理器"""
    
    def __init__(self, config_path: str = "cluster/cluster_config.json"):
        self.config_path = Path(config_path)
        self.nodes: Dict[str, NodeInfo] = {}
        self.config = self._load_config()
        self.encryption_key = self._get_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        self.websocket_server = None
        self.running = False
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('cluster/cluster_manager.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def _load_config(self) -> Dict[str, Any]:
        """加载集群配置"""
        default_config = {
            "cluster_name": "TimeNest-Cluster",
            "manager_port": 8765,
            "heartbeat_interval": 30,
            "node_timeout": 90,
            "max_nodes": 100,
            "encryption_enabled": True,
            "authentication": {
                "enabled": True,
                "admin_token": "admin_secret_token_change_me"
            },
            "monitoring": {
                "enabled": True,
                "metrics_retention_days": 30,
                "alert_thresholds": {
                    "cpu_usage": 80,
                    "memory_usage": 85,
                    "disk_usage": 90,
                    "response_time": 5000
                }
            }
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                self.logger.error(f"加载配置失败: {e}")
                
        # 创建默认配置文件
        self.config_path.parent.mkdir(exist_ok=True)
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
            
        return default_config
    
    def _get_encryption_key(self) -> bytes:
        """获取或生成加密密钥"""
        key_file = Path("cluster/cluster.key")
        
        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            key_file.parent.mkdir(exist_ok=True)
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def encrypt_message(self, message: str) -> str:
        """加密消息"""
        if self.config.get("encryption_enabled", True):
            return self.cipher.encrypt(message.encode()).decode()
        return message
    
    def decrypt_message(self, encrypted_message: str) -> str:
        """解密消息"""
        if self.config.get("encryption_enabled", True):
            try:
                return self.cipher.decrypt(encrypted_message.encode()).decode()
            except Exception:
                raise ValueError("解密失败")
        return encrypted_message
    
    async def start_server(self):
        """启动集群管理服务器"""
        self.running = True
        port = self.config.get("manager_port", 8765)
        
        self.logger.info(f"启动集群管理器，端口: {port}")
        
        # 启动WebSocket服务器
        self.websocket_server = await websockets.serve(
            self.handle_client,
            "0.0.0.0",
            port
        )
        
        # 启动后台任务
        asyncio.create_task(self.heartbeat_monitor())
        asyncio.create_task(self.metrics_collector())
        asyncio.create_task(self.health_checker())
        
        self.logger.info("集群管理器启动完成")
        
    async def stop_server(self):
        """停止集群管理服务器"""
        self.running = False
        if self.websocket_server:
            self.websocket_server.close()
            await self.websocket_server.wait_closed()
        self.logger.info("集群管理器已停止")
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_ip = websocket.remote_address[0]
        self.logger.info(f"新客户端连接: {client_ip}")
        
        try:
            async for message in websocket:
                try:
                    # 解密消息
                    decrypted_message = self.decrypt_message(message)
                    data = json.loads(decrypted_message)
                    
                    # 处理消息
                    response = await self.process_message(data, websocket)
                    
                    # 发送响应
                    if response:
                        encrypted_response = self.encrypt_message(json.dumps(response))
                        await websocket.send(encrypted_response)
                        
                except Exception as e:
                    self.logger.error(f"处理消息失败: {e}")
                    error_response = {
                        "type": "error",
                        "message": str(e),
                        "timestamp": time.time()
                    }
                    encrypted_error = self.encrypt_message(json.dumps(error_response))
                    await websocket.send(encrypted_error)
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"客户端断开连接: {client_ip}")
        except Exception as e:
            self.logger.error(f"客户端连接错误: {e}")
    
    async def process_message(self, data: Dict[str, Any], websocket) -> Optional[Dict[str, Any]]:
        """处理客户端消息"""
        message_type = data.get("type")
        
        if message_type == "register":
            return await self.handle_node_register(data, websocket)
        elif message_type == "heartbeat":
            return await self.handle_heartbeat(data)
        elif message_type == "metrics":
            return await self.handle_metrics(data)
        elif message_type == "command":
            return await self.handle_command(data)
        elif message_type == "config_request":
            return await self.handle_config_request(data)
        else:
            return {
                "type": "error",
                "message": f"未知消息类型: {message_type}",
                "timestamp": time.time()
            }
    
    async def handle_node_register(self, data: Dict[str, Any], websocket) -> Dict[str, Any]:
        """处理节点注册"""
        try:
            node_info = NodeInfo(
                node_id=data["node_id"],
                hostname=data["hostname"],
                ip_address=data["ip_address"],
                port=data["port"],
                version=data["version"],
                status="online",
                last_heartbeat=time.time(),
                cpu_usage=data.get("cpu_usage", 0),
                memory_usage=data.get("memory_usage", 0),
                disk_usage=data.get("disk_usage", 0),
                active_users=data.get("active_users", 0),
                uptime=data.get("uptime", 0)
            )
            
            self.nodes[node_info.node_id] = node_info
            self.logger.info(f"节点注册成功: {node_info.node_id} ({node_info.hostname})")
            
            return {
                "type": "register_response",
                "status": "success",
                "node_id": node_info.node_id,
                "cluster_config": self.get_cluster_config(),
                "timestamp": time.time()
            }
            
        except Exception as e:
            self.logger.error(f"节点注册失败: {e}")
            return {
                "type": "register_response",
                "status": "error",
                "message": str(e),
                "timestamp": time.time()
            }
    
    async def handle_heartbeat(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理心跳消息"""
        node_id = data.get("node_id")
        
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.last_heartbeat = time.time()
            node.status = "online"
            
            # 更新性能指标
            if "cpu_usage" in data:
                node.cpu_usage = data["cpu_usage"]
            if "memory_usage" in data:
                node.memory_usage = data["memory_usage"]
            if "disk_usage" in data:
                node.disk_usage = data["disk_usage"]
            if "active_users" in data:
                node.active_users = data["active_users"]
            if "uptime" in data:
                node.uptime = data["uptime"]
            
            return {
                "type": "heartbeat_response",
                "status": "success",
                "timestamp": time.time()
            }
        else:
            return {
                "type": "heartbeat_response",
                "status": "error",
                "message": "节点未注册",
                "timestamp": time.time()
            }
    
    async def handle_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理指标数据"""
        node_id = data.get("node_id")
        metrics = data.get("metrics", {})
        
        # 保存指标数据到文件或数据库
        await self.save_metrics(node_id, metrics)
        
        return {
            "type": "metrics_response",
            "status": "success",
            "timestamp": time.time()
        }
    
    async def handle_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理命令"""
        command = data.get("command")
        target_nodes = data.get("target_nodes", [])
        
        if command == "get_cluster_status":
            return await self.get_cluster_status()
        elif command == "restart_node":
            return await self.restart_nodes(target_nodes)
        elif command == "update_config":
            return await self.update_node_config(target_nodes, data.get("config", {}))
        elif command == "shutdown_node":
            return await self.shutdown_nodes(target_nodes)
        else:
            return {
                "type": "command_response",
                "status": "error",
                "message": f"未知命令: {command}",
                "timestamp": time.time()
            }
    
    async def handle_config_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理配置请求"""
        node_id = data.get("node_id")
        
        # 返回节点特定配置
        config = self.get_node_config(node_id)
        
        return {
            "type": "config_response",
            "config": config,
            "timestamp": time.time()
        }
    
    def get_cluster_config(self) -> Dict[str, Any]:
        """获取集群配置"""
        return {
            "cluster_name": self.config["cluster_name"],
            "heartbeat_interval": self.config["heartbeat_interval"],
            "encryption_enabled": self.config["encryption_enabled"]
        }
    
    def get_node_config(self, node_id: str) -> Dict[str, Any]:
        """获取节点配置"""
        # 这里可以根据节点ID返回特定配置
        return {
            "node_id": node_id,
            "sync_interval": 300,
            "log_level": "INFO",
            "features": {
                "auto_update": True,
                "remote_control": True,
                "metrics_collection": True
            }
        }
    
    async def heartbeat_monitor(self):
        """心跳监控"""
        while self.running:
            current_time = time.time()
            timeout = self.config.get("node_timeout", 90)
            
            for node_id, node in list(self.nodes.items()):
                if current_time - node.last_heartbeat > timeout:
                    if node.status != "offline":
                        node.status = "offline"
                        self.logger.warning(f"节点离线: {node_id}")
            
            await asyncio.sleep(30)  # 每30秒检查一次
    
    async def metrics_collector(self):
        """指标收集器"""
        while self.running:
            # 收集集群整体指标
            cluster_metrics = {
                "timestamp": time.time(),
                "total_nodes": len(self.nodes),
                "online_nodes": len([n for n in self.nodes.values() if n.status == "online"]),
                "offline_nodes": len([n for n in self.nodes.values() if n.status == "offline"]),
                "total_users": sum(n.active_users for n in self.nodes.values()),
                "avg_cpu_usage": sum(n.cpu_usage for n in self.nodes.values()) / len(self.nodes) if self.nodes else 0,
                "avg_memory_usage": sum(n.memory_usage for n in self.nodes.values()) / len(self.nodes) if self.nodes else 0
            }
            
            # 保存集群指标
            await self.save_cluster_metrics(cluster_metrics)
            
            await asyncio.sleep(60)  # 每分钟收集一次
    
    async def health_checker(self):
        """健康检查"""
        while self.running:
            for node in self.nodes.values():
                if node.status == "online":
                    # 检查性能阈值
                    alerts = []
                    thresholds = self.config["monitoring"]["alert_thresholds"]
                    
                    if node.cpu_usage > thresholds["cpu_usage"]:
                        alerts.append(f"CPU使用率过高: {node.cpu_usage}%")
                    
                    if node.memory_usage > thresholds["memory_usage"]:
                        alerts.append(f"内存使用率过高: {node.memory_usage}%")
                    
                    if node.disk_usage > thresholds["disk_usage"]:
                        alerts.append(f"磁盘使用率过高: {node.disk_usage}%")
                    
                    if alerts:
                        await self.send_alert(node.node_id, alerts)
            
            await asyncio.sleep(120)  # 每2分钟检查一次
    
    async def save_metrics(self, node_id: str, metrics: Dict[str, Any]):
        """保存节点指标"""
        metrics_dir = Path("cluster/metrics")
        metrics_dir.mkdir(exist_ok=True)
        
        date_str = datetime.now().strftime("%Y-%m-%d")
        metrics_file = metrics_dir / f"{node_id}_{date_str}.json"
        
        # 追加指标数据
        if metrics_file.exists():
            with open(metrics_file, 'r', encoding='utf-8') as f:
                existing_metrics = json.load(f)
        else:
            existing_metrics = []
        
        existing_metrics.append({
            "timestamp": time.time(),
            "metrics": metrics
        })
        
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(existing_metrics, f, indent=2, ensure_ascii=False)
    
    async def save_cluster_metrics(self, metrics: Dict[str, Any]):
        """保存集群指标"""
        metrics_dir = Path("cluster/metrics")
        metrics_dir.mkdir(exist_ok=True)
        
        date_str = datetime.now().strftime("%Y-%m-%d")
        metrics_file = metrics_dir / f"cluster_{date_str}.json"
        
        if metrics_file.exists():
            with open(metrics_file, 'r', encoding='utf-8') as f:
                existing_metrics = json.load(f)
        else:
            existing_metrics = []
        
        existing_metrics.append(metrics)
        
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(existing_metrics, f, indent=2, ensure_ascii=False)
    
    async def send_alert(self, node_id: str, alerts: List[str]):
        """发送告警"""
        alert_data = {
            "node_id": node_id,
            "alerts": alerts,
            "timestamp": time.time(),
            "severity": "warning"
        }
        
        self.logger.warning(f"节点告警 {node_id}: {', '.join(alerts)}")
        
        # 这里可以集成邮件、短信、钉钉等告警通道
        await self.save_alert(alert_data)
    
    async def save_alert(self, alert_data: Dict[str, Any]):
        """保存告警记录"""
        alerts_dir = Path("cluster/alerts")
        alerts_dir.mkdir(exist_ok=True)
        
        date_str = datetime.now().strftime("%Y-%m-%d")
        alerts_file = alerts_dir / f"alerts_{date_str}.json"
        
        if alerts_file.exists():
            with open(alerts_file, 'r', encoding='utf-8') as f:
                existing_alerts = json.load(f)
        else:
            existing_alerts = []
        
        existing_alerts.append(alert_data)
        
        with open(alerts_file, 'w', encoding='utf-8') as f:
            json.dump(existing_alerts, f, indent=2, ensure_ascii=False)
    
    async def get_cluster_status(self) -> Dict[str, Any]:
        """获取集群状态"""
        return {
            "type": "cluster_status",
            "cluster_name": self.config["cluster_name"],
            "total_nodes": len(self.nodes),
            "online_nodes": len([n for n in self.nodes.values() if n.status == "online"]),
            "offline_nodes": len([n for n in self.nodes.values() if n.status == "offline"]),
            "nodes": [asdict(node) for node in self.nodes.values()],
            "timestamp": time.time()
        }
    
    async def restart_nodes(self, node_ids: List[str]) -> Dict[str, Any]:
        """重启节点"""
        results = {}
        
        for node_id in node_ids:
            if node_id in self.nodes:
                # 发送重启命令到节点
                # 这里需要实现向节点发送命令的逻辑
                results[node_id] = "restart_sent"
                self.logger.info(f"发送重启命令到节点: {node_id}")
            else:
                results[node_id] = "node_not_found"
        
        return {
            "type": "restart_response",
            "results": results,
            "timestamp": time.time()
        }
    
    async def update_node_config(self, node_ids: List[str], config: Dict[str, Any]) -> Dict[str, Any]:
        """更新节点配置"""
        results = {}
        
        for node_id in node_ids:
            if node_id in self.nodes:
                # 发送配置更新命令到节点
                results[node_id] = "config_update_sent"
                self.logger.info(f"发送配置更新到节点: {node_id}")
            else:
                results[node_id] = "node_not_found"
        
        return {
            "type": "config_update_response",
            "results": results,
            "timestamp": time.time()
        }
    
    async def shutdown_nodes(self, node_ids: List[str]) -> Dict[str, Any]:
        """关闭节点"""
        results = {}
        
        for node_id in node_ids:
            if node_id in self.nodes:
                # 发送关闭命令到节点
                results[node_id] = "shutdown_sent"
                self.logger.info(f"发送关闭命令到节点: {node_id}")
            else:
                results[node_id] = "node_not_found"
        
        return {
            "type": "shutdown_response",
            "results": results,
            "timestamp": time.time()
        }

# 主函数
async def main():
    """主函数"""
    manager = ClusterManager()
    
    try:
        await manager.start_server()
        
        # 保持服务器运行
        while manager.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务器...")
    finally:
        await manager.stop_server()

if __name__ == "__main__":
    asyncio.run(main())
